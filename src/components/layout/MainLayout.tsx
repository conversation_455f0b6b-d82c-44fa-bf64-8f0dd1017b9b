'use client'

import React, { useEffect } from 'react'
import { useAppStore } from '@/lib/store'
import { X, Plus, FileText } from 'lucide-react'
import WorkArea from './WorkArea'
import DynamicBackground from '../ui/DynamicBackground'
import { cn } from '@/lib/utils'

const MainLayout: React.FC = () => {
  const {
    tabs,
    activeMiddleTab,
    setActiveMiddleTab,
    isPureModeEnabled,
    setPureModeEnabled
  } = useAppStore()

  // 移除了右侧面板拖拽相关逻辑

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '1':
            e.preventDefault()
            setActiveMiddleTab('original')
            break
          case '2':
            e.preventDefault()
            setActiveMiddleTab('notes')
            break
          // case '3': diff功能已移除
          case 'j':
            e.preventDefault()
            // 聚焦AI助手输入框
            const chatInput = document.querySelector('#chat-form input') as HTMLInputElement
            if (chatInput) {
              chatInput.focus()
            }
            break
          case 'p':
            e.preventDefault()
            // 切换沉淀模式
            setPureModeEnabled(!isPureModeEnabled)
            break
        }
      }
      
      // Tab键切换
      if (e.key === 'Tab' && !e.ctrlKey && !e.metaKey && !e.altKey) {
        const currentTabs = ['original', 'notes']
        
        const currentIndex = currentTabs.indexOf(activeMiddleTab)
        if (currentIndex !== -1) {
          e.preventDefault()
          const nextIndex = (currentIndex + 1) % currentTabs.length
          setActiveMiddleTab(currentTabs[nextIndex] as 'original' | 'notes' | 'diff')
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [activeMiddleTab, setActiveMiddleTab])

  // 判断当前活跃标签页是否为空的新建标签页
  const activeTab = tabs.find(tab => tab.id === useAppStore.getState().activeTabId)
  const isEmptyNewTab = activeTab &&
    activeTab.sourceData === '' &&
    activeTab.originalContent === '' &&
    activeTab.aiNoteMarkdown === ''

  return (
    <div className={`h-screen adaptive-light flex flex-col relative ${isPureModeEnabled ? 'pure-mode' : ''}`}>
      {/* 动态背景 - 只在没有标签页或空标签页时显示，沉淀模式下隐藏 */}
      {(tabs.length === 0 || isEmptyNewTab) && !isPureModeEnabled && <DynamicBackground />}

      {/* 动态标签栏 - 只在有标签页时显示 */}
      {tabs.length > 0 && (
        <div className="glass-effect-strong border-b border-border/50 flex items-center px-6 shadow-sm flex-shrink-0">
          {/* Chrome风格标签页布局 */}
          <div className="flex items-center">
            {/* 标签页列表 - 紧密排列 */}
            <div className="flex items-center overflow-x-auto">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-t-xl cursor-pointer transition-all duration-200 flex-shrink-0 ${
                    tab.id === useAppStore.getState().activeTabId
                      ? 'glass-effect text-primary border-b-2 border-primary shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                  }`}
                  onClick={() => useAppStore.getState().setActiveTab(tab.id)}
                >
                  <span className="text-sm font-medium truncate max-w-32">
                    {tab.title}
                  </span>
                  {tab.isLoading && (
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  )}
                  {tab.aiAnalyzing && (
                    <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      useAppStore.getState().removeTab(tab.id)
                    }}
                    className="p-1 hover:bg-gray-200/80 rounded-full transition-colors"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))}
            </div>

            {/* Chrome风格新建标签页按钮 - 紧贴最右侧标签页 */}
            <button
              onClick={() => {
                useAppStore.getState().addTab({
                  title: '新标签页',
                  sourceType: 'text',
                  sourceData: '',
                  originalContent: '',
                  aiNoteMarkdown: '',
                  isLoading: false
                })
              }}
              className="ml-1 p-2 hover:bg-white/60 rounded-lg transition-all duration-200 flex-shrink-0"
              title="新建标签页"
            >
              <Plus size={14} className="text-gray-500" />
            </button>
          </div>

          {/* 沉淀模式切换按钮 */}
          {tabs.length > 0 && !isEmptyNewTab && (
            <div className="ml-auto flex items-center">
              <button
                onClick={() => setPureModeEnabled(!isPureModeEnabled)}
                className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 ${
                  isPureModeEnabled
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                title={isPureModeEnabled ? '退出沉淀模式' : '进入沉淀模式'}
              >
                {isPureModeEnabled ? '📖 沉淀模式' : '🔧 编辑模式'}
              </button>
            </div>
          )}
        </div>
      )}

      {/* 主要内容区域 - 单栏布局 */}
      <div className="flex-1 flex overflow-hidden min-h-0 p-2">

        {/* 中栏 - 多Tab内容区 */}
        <div
          className="flex flex-col min-h-0 liquid-panel"
          style={{
            width: '100%'
          }}
        >
          {tabs.length > 0 && !isEmptyNewTab && !isPureModeEnabled && (
            <div className="border-b border-border/50 px-4 lg:hidden">
              <div className="flex items-center">
                <button
                  onClick={() => setActiveMiddleTab('original')}
                  className={cn(
                    "px-4 py-2 text-sm font-medium transition-colors relative",
                    activeMiddleTab === 'original'
                      ? "text-primary after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-primary"
                      : "text-gray-600 hover:text-gray-900"
                  )}
                >
                  <FileText className="inline-block w-4 h-4 mr-1" />
                  原文
                </button>
                <button
                  onClick={() => setActiveMiddleTab('notes')}
                  className={cn(
                    "px-4 py-2 text-sm font-medium transition-colors relative",
                    activeMiddleTab === 'notes'
                      ? "text-primary after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-primary"
                      : "text-gray-600 hover:text-gray-900"
                  )}
                >
                  <FileText className="inline-block w-4 h-4 mr-1" />
                  结构化笔记
                </button>
              </div>
            </div>
          )}
          
          <WorkArea />
        </div>

        {/* AI助手栏已完全移除，专注于内容阅读 */}
      </div>
    </div>
  )
}

export default MainLayout
