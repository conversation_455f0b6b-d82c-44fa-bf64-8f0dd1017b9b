/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --radius: 0.75rem;

  /* 现代化背景系统 - 基于HTML参考文件 */
  --bg-primary: #fafafa;
  --bg-secondary: #ffffff;
  --bg-glass: rgba(255, 255, 255, 0.7);
  --bg-glass-strong: rgba(255, 255, 255, 0.85);
  --bg-glass-subtle: rgba(255, 255, 255, 0.5);

  /* HTML参考文件的新增变量 */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --accent: #f59e0b;
  --glass: rgba(255, 255, 255, 0.7);
  --glass-dark: rgba(30, 41, 59, 0.03);
  --glass-border: rgba(148, 163, 184, 0.2);
  --primary-light: #818cf8;
  --primary-dark: #4f46e5;

  /* 基础色彩 */
  --background: #FFFFFF;
  --foreground: #0F172A;
  --card: #FFFFFF;
  --card-foreground: #0F172A;
  --popover: #FFFFFF;
  --popover-foreground: #0F172A;

  /* 主色调系统 - 优化渐变 */
  --primary: #6366F1;
  --primary-foreground: #FFFFFF;
  --primary-50: #EEF2FF;
  --primary-100: #E0E7FF;
  --primary-200: #C7D2FE;
  --primary-300: #A5B4FC;
  --primary-400: #818CF8;
  --primary-500: #6366F1;
  --primary-600: #4F46E5;
  --primary-700: #4338CA;
  --primary-800: #3730A3;
  --primary-900: #312E81;
  --primary-950: #1E1B4B;
  --accent-light: #818cf8;

  /* 语义色彩 */
  --secondary: #F8FAFC;
  --secondary-foreground: #0F172A;
  --muted: #F1F5F9;
  --muted-foreground: #64748B;
  --accent: #F1F5F9;
  --accent-foreground: #0F172A;
  --destructive: #EF4444;

  /* 边框和输入 */
  --border: rgba(226, 232, 240, 0.5);
  --border-strong: #E2E8F0;
  --input: #E2E8F0;
  --ring: #6366F1;

  /* 现代化阴影系统 - 基于HTML参考文件 */
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.08);
  --shadow-glass: 0 8px 32px rgba(99, 102, 241, 0.1);

  /* 毛玻璃效果 */
  --blur: 20px;
  --blur-subtle: 10px;

  /* 现代化渐变色 */
  --gradient-from: #6366F1;
  --gradient-via: #8B5CF6;
  --gradient-to: #EC4899;

  /* 品牌色系 */
  --brand-50: #F0F9FF;
  --brand-100: #E0F2FE;
  --brand-200: #BAE6FD;
  --brand-300: #7DD3FC;
  --brand-400: #38BDF8;
  --brand-500: #0EA5E9;
  --brand-600: #0284C7;
  --brand-700: #0369A1;
  --brand-800: #075985;
  --brand-900: #0C4A6E;
  --brand-950: #082F49;

  /* 侧边栏 */
  --sidebar: #FAFAFA;
  --sidebar-foreground: #0F172A;
  --sidebar-primary: #6366F1;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #F1F5F9;
  --sidebar-accent-foreground: #0F172A;
  --sidebar-border: #E2E8F0;
  --sidebar-ring: #6366F1;
}

.dark {
  --background: #FFFFFF;
  --foreground: #0F172A;
  --card: #FFFFFF;
  --card-foreground: #0F172A;
  --popover: #FFFFFF;
  --popover-foreground: #0F172A;
  --primary: #6366F1;
  --primary-foreground: #FFFFFF;
  --primary-50: #1E1B4B;
  --primary-100: #312E81;
  --primary-200: #3730A3;
  --primary-300: #4338CA;
  --primary-400: #4F46E5;
  --primary-500: #6366F1;
  --primary-600: #818CF8;
  --primary-700: #A5B4FC;
  --primary-800: #C7D2FE;
  --primary-900: #E0E7FF;
  --primary-950: #EEF2FF;
  --secondary: #F8FAFC;
  --secondary-foreground: #0F172A;
  --muted: #F1F5F9;
  --muted-foreground: #64748B;
  --accent: #F1F5F9;
  --accent-foreground: #0F172A;
  --destructive: #EF4444;
  --border: #E2E8F0;
  --input: #E2E8F0;
  --ring: #6366F1;

  /* 深色模式渐变色 */
  --gradient-from: #4F46E5;
  --gradient-via: #7C3AED;
  --gradient-to: #DB2777;

  /* 深色模式品牌色 */
  --brand-50: #082F49;
  --brand-100: #0C4A6E;
  --brand-200: #075985;
  --brand-300: #0369A1;
  --brand-400: #0284C7;
  --brand-500: #0EA5E9;
  --brand-600: #38BDF8;
  --brand-700: #7DD3FC;
  --brand-800: #BAE6FD;
  --brand-900: #E0F2FE;
  --brand-950: #F0F9FF;

  /* 深色模式侧边栏 */
  --sidebar: #FAFAFA;
  --sidebar-foreground: #0F172A;
  --sidebar-primary: #6366F1;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #F1F5F9;
  --sidebar-accent-foreground: #0F172A;
  --sidebar-border: #E2E8F0;
  --sidebar-ring: #6366F1;
}

/* 基础样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', 'Inter', sans-serif;
  background: var(--bg-primary);
  color: var(--foreground);
  line-height: 1.6;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: oldstyle-nums;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* 现代化滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgb(203 213 225 / 0.5) transparent;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(203 213 225 / 0.5), rgb(156 163 175 / 0.5));
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(156 163 175 / 0.7), rgb(107 114 128 / 0.7));
}

/* 选择文本样式 */
::selection {
  background-color: rgb(99 102 241 / 0.2);
  color: rgb(99 102 241);
}

::-moz-selection {
  background-color: rgb(99 102 241 / 0.2);
  color: rgb(99 102 241);
}

/* 现代化工具类 */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225 / 0.5) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, rgb(203 213 225 / 0.5), rgb(156 163 175 / 0.5));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, rgb(156 163 175 / 0.7), rgb(107 114 128 / 0.7));
  }

  /* 现代化文本渐变 */
  .text-gradient {
    background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 玻璃拟态效果 */
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-effect-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* 现代化阴影 */
  .shadow-glow-primary {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }

  .shadow-glow-pink {
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
  }

  /* 动画延迟工具类 */
  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  .animate-delay-500 {
    animation-delay: 500ms;
  }

  /* Liquid Glass 设计系统 - 优化可读性 */
  .liquid-glass {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(255, 255, 255, 0.85) 100%
    );
    backdrop-filter: blur(12px) saturate(120%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.06),
      0 2px 8px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    position: relative;
    transition: all 0.2s ease-out;
  }

  .liquid-glass:hover {
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.08),
      0 3px 10px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.4);
  }

  /* 液体面板 - 优化可读性 */
  .liquid-panel {
    background: white;
    border: 1px solid rgba(229, 231, 235, 0.8);
    border-radius: 18px;
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.04),
      0 3px 10px rgba(0, 0, 0, 0.03);
    position: relative;
  }

  /* 静态光谱高光效果 - 移除动画以提升可读性 */
  .spectrum-highlight {
    position: relative;
    overflow: hidden;
  }

  .spectrum-highlight::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.02) 0%,
      rgba(147, 51, 234, 0.02) 25%,
      rgba(236, 72, 153, 0.02) 50%,
      rgba(245, 158, 11, 0.02) 75%,
      rgba(34, 197, 94, 0.02) 100%
    );
    pointer-events: none;
  }

  /* 简化的流体运动特性 - 减少干扰 */
  .fluid-motion {
    transition: all 0.2s ease-out;
  }

  .fluid-motion:hover {
    transform: translateY(-1px);
  }

  /* 简化的凝胶柔韧性 - 减少干扰 */
  .gel-flexibility {
    transition: all 0.15s ease-out;
  }

  .gel-flexibility:active {
    transform: scale(0.99);
  }

  /* 智能适应颜色系统 */
  .adaptive-light {
    background: white;
  }

  .adaptive-dark {
    background: linear-gradient(135deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.9) 100%
    );
  }

  @media (prefers-color-scheme: dark) {
    .liquid-glass {
      background: white;
      border-color: rgba(229, 231, 235, 0.8);
    }

    .liquid-panel {
      background: white;
      border-color: rgba(229, 231, 235, 0.8);
    }
  }

  /* Bento Grid 样式 - 更新为Liquid Glass风格 */
  .bento-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 20px;
  }

  .bento-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  /* 现代化卡片阴影 */
  .card-shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.08);
  }

  .card-shadow-medium {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.12);
  }

  .card-shadow-strong {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 12px rgba(0, 0, 0, 0.16);
  }

  /* 文本截断工具类 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 加载动画 */
@keyframes dot-flashing {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.dot-flashing {
  animation: dot-flashing 1.4s infinite ease-in-out;
}

.dot-flashing:nth-child(2) {
  animation-delay: 0.2s;
}

.dot-flashing:nth-child(3) {
  animation-delay: 0.4s;
}

/* 现代化脉冲动画 */
@keyframes modern-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.modern-pulse {
  animation: modern-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 渐入动画 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out;
}

/* 卡片展开动画 */
@keyframes card-expand {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 2000px;
    opacity: 1;
  }
}

.card-expand {
  animation: card-expand 0.3s ease-out;
}

/* 骨架屏动画 */
@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.skeleton {
  animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: rgb(229 231 235);
  border-radius: 0.375rem;
}

/* 打字机效果 */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typing-effect {
  overflow: hidden;
  border-right: 2px solid #3B82F6;
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: #3B82F6;
  }
}

/* 增强 prose 样式 - 优化字体大小和间距 */
.prose {
  color: #111827;
  font-size: 0.875rem; /* 14px - 提高内容密度 */
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: #111827;
  font-weight: 600;
}

.prose h1 {
  font-size: 1.25rem; /* 20px - 从默认的更大尺寸减小 */
  line-height: 1.3;
  margin-top: 0;
  margin-bottom: 1rem;
}

.prose h2 {
  font-size: 1.125rem; /* 18px - 减小尺寸 */
  line-height: 1.3;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.prose h3 {
  font-size: 1rem; /* 16px - 减小尺寸 */
  line-height: 1.3;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.prose p {
  color: #111827;
  line-height: 1.5; /* 从1.625减小到1.5 */
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem; /* 确保一致的字体大小 */
}

.prose a {
  color: #2563eb;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.prose a:hover {
  text-decoration: underline;
}

.prose strong {
  color: #111827;
  font-weight: 600;
}

.prose em {
  color: #111827;
  font-style: italic;
}

.prose code {
  color: #2563eb;
  background-color: #f3f4f6;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  border: 1px solid #e5e7eb;
}

.prose pre {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.prose pre code {
  background-color: transparent;
  border: 0;
  padding: 0;
}

.prose blockquote {
  color: #6b7280;
  border-left: 4px solid #bfdbfe;
  padding-left: 1.5rem;
  font-style: italic;
  background-color: #f9fafb;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0 0.25rem 0.25rem 0;
}

.prose ul,
.prose ol {
  color: #111827;
}

.prose li {
  color: #111827;
  line-height: 1.625;
}

.prose img {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  max-width: 100%;
  height: auto;
}

.prose hr {
  border-color: #e5e7eb;
  margin: 2rem 0;
  border-top-width: 2px;
}

.prose table {
  border-collapse: collapse;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.prose th {
  border: 1px solid #e5e7eb;
  background-color: #f3f4f6;
  padding: 0.75rem;
  font-weight: 600;
  text-align: left;
}

.prose td {
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  vertical-align: top;
}

/* 结构化笔记专用样式 - 优化字体大小提升空间利用率 */
.ai-note-content {
  line-height: 1.6;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: oldstyle-nums;
  font-size: 0.875rem; /* 14px - 提高内容密度 */
}

.ai-note-content h2 {
  color: #1f2937;
  font-size: 1rem; /* 16px - 从18px减小 */
  font-weight: 600;
  margin-top: 1.25rem; /* 减少间距 */
  margin-bottom: 0.5rem; /* 减少间距 */
  padding-bottom: 0.375rem;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  line-height: 1.3; /* 紧凑行高 */
}

.ai-note-content h2:first-child {
  margin-top: 0;
}

.ai-note-content h3 {
  color: #374151;
  font-size: 0.875rem; /* 14px - 从16px减小 */
  font-weight: 600;
  margin-top: 1rem; /* 减少间距 */
  margin-bottom: 0.375rem; /* 减少间距 */
  line-height: 1.3;
}

.ai-note-content ul {
  margin: 0.5rem 0; /* 减少间距 */
  padding-left: 1rem; /* 减少缩进 */
}

.ai-note-content li {
  margin: 0.25rem 0; /* 减少间距 */
  line-height: 1.5;
  color: #4b5563;
  font-size: 0.875rem; /* 确保一致的字体大小 */
}

.ai-note-content li::marker {
  color: #6366f1;
}

.ai-note-content p {
  margin: 0.5rem 0; /* 减少间距 */
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.875rem; /* 确保一致的字体大小 */
}

.ai-note-content strong {
  color: #1f2937;
  font-weight: 600;
}

.ai-note-content em {
  color: #6366f1;
  font-style: normal;
  font-weight: 500;
}

.ai-note-content code {
  background-color: #f1f5f9;
  color: #3730a3;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  border: 1px solid #e2e8f0;
}

/* 结构化笔记分割线样式 */
.ai-note-content hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, #e5e7eb, transparent);
  margin: 1.5rem 0;
}

/* 结构化笔记引用块样式 */
.ai-note-content blockquote {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-left: 4px solid #6366f1;
  padding: 1rem 1.25rem;
  margin: 1rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
  font-style: normal;
  color: #475569;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.ai-note-content blockquote:hover {
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  transform: translateY(-1px);
}

/* 现代化流式输出动画 */
.ai-note-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 优化代码块样式 */
.ai-note-content pre {
  background: linear-gradient(135deg, #1e293b, #334155);
  border: 1px solid #475569;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.ai-note-content pre:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* ========== 现代化背景装饰系统 ========== */
.bg-decoration {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.4;
  animation: float 20s infinite ease-in-out;
}

.bg-circle:nth-child(1) {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  top: -200px;
  right: -100px;
}

.bg-circle:nth-child(2) {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  bottom: -150px;
  left: -50px;
  animation-delay: -5s;
}

.bg-circle:nth-child(3) {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -10s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  25% { transform: translate(-30px, 30px) scale(1.05); }
  50% { transform: translate(30px, -30px) scale(0.95); }
  75% { transform: translate(-20px, -20px) scale(1.02); }
}

/* ========== 毛玻璃效果系统 ========== */
.glass-effect {
  background: var(--bg-glass);
  backdrop-filter: blur(var(--blur));
  -webkit-backdrop-filter: blur(var(--blur));
  border: 1px solid var(--border);
}

.glass-effect-strong {
  background: var(--bg-glass-strong);
  backdrop-filter: blur(var(--blur));
  -webkit-backdrop-filter: blur(var(--blur));
  border: 1px solid var(--border);
}

.glass-effect-subtle {
  background: var(--bg-glass-subtle);
  backdrop-filter: blur(var(--blur-subtle));
  -webkit-backdrop-filter: blur(var(--blur-subtle));
  border: 1px solid var(--border);
}

/* ========== 现代化卡片系统 ========== */
.modern-card {
  background: var(--bg-glass);
  backdrop-filter: blur(var(--blur));
  -webkit-backdrop-filter: blur(var(--blur));
  border: 1px solid var(--border);
  border-radius: 16px;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary);
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-card:hover::before {
  opacity: 1;
}

/* ========== 增强的Bento Grid布局 ========== */
.enhanced-bento-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
}

.enhanced-bento-card {
  background: var(--bg-glass);
  backdrop-filter: blur(var(--blur));
  -webkit-backdrop-filter: blur(var(--blur));
  border: 1px solid var(--border);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.enhanced-bento-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary);
}

.enhanced-bento-card.large {
  grid-column: span 2;
}

.enhanced-bento-card.tall {
  grid-row: span 2;
}

/* ========== 现代化侧边栏样式 ========== */
.modern-sidebar {
  background: var(--bg-glass);
  backdrop-filter: blur(var(--blur));
  -webkit-backdrop-filter: blur(var(--blur));
  border-right: 1px solid var(--border);
  transition: all 0.3s ease;
}

/* ========== 现代化输入框样式 ========== */
.modern-input {
  background: var(--bg-glass-strong);
  backdrop-filter: blur(var(--blur-subtle));
  -webkit-backdrop-filter: blur(var(--blur-subtle));
  border: 1px solid var(--border);
  transition: all 0.3s ease;
}

.modern-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background: var(--bg-secondary);
}

/* ========== 响应式设计优化 ========== */
@media (max-width: 768px) {
  .bg-circle {
    opacity: 0.2;
  }

  .bg-circle:nth-child(1) {
    width: 200px;
    height: 200px;
  }

  .bg-circle:nth-child(2) {
    width: 150px;
    height: 150px;
  }

  .bg-circle:nth-child(3) {
    width: 120px;
    height: 120px;
  }

  .enhanced-bento-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0.5rem;
  }

  .enhanced-bento-card.large {
    grid-column: span 1;
  }

  .modern-card {
    border-radius: 12px;
  }

  .glass-effect,
  .glass-effect-strong,
  .glass-effect-subtle {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

/* ========== HTML参考文件的新增样式 ========== */

/* 动态背景动画 */
@keyframes float {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  25% {
    transform: translate(-50px, 50px) scale(1.1);
  }
  50% {
    transform: translate(50px, -30px) scale(0.9);
  }
  75% {
    transform: translate(-30px, -50px) scale(1.05);
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

/* 旋转动画 */
@keyframes spin {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(180deg);
  }
  100% {
    transform: scale(1) rotate(360deg);
  }
}

/* 动态背景组件样式 */
.bg-animation {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 0;
  overflow: hidden;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.15;
  animation: float 20s infinite ease-in-out;
}

.gradient-orb:nth-child(1) {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, #6366f1 0%, transparent 70%);
  top: -200px;
  right: -100px;
}

.gradient-orb:nth-child(2) {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #f59e0b 0%, transparent 70%);
  bottom: -150px;
  left: -50px;
  animation-delay: -5s;
}

.gradient-orb:nth-child(3) {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, #818cf8 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -10s;
}

/* 网格背景 */
.grid-bg {
  position: fixed;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(148, 163, 184, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(148, 163, 184, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 1;
}

/* 现代化应用图标样式 */
.app-icon {
  width: 120px;
  height: 120px;
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.app-icon:hover {
  transform: scale(1.1);
}

.icon-container {
  width: 100%;
  height: 100%;
  background: var(--glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.icon-container::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, var(--primary), var(--primary-light), var(--accent));
  border-radius: 30px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.app-icon:hover .icon-container::before {
  opacity: 0.8;
}

.app-icon:hover .icon-container {
  border-color: transparent;
  background: rgba(255, 255, 255, 0.9);
}

/* 数据流图标样式 */
.data-flow {
  width: 100%;
  height: 100%;
  position: relative;
}

.node {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--primary);
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
}

.node:nth-child(1) {
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
}

.node:nth-child(2) {
  top: 40%;
  left: 20%;
}

.node:nth-child(3) {
  top: 40%;
  right: 20%;
}

.node:nth-child(4) {
  bottom: 10%;
  left: 30%;
}

.node:nth-child(5) {
  bottom: 10%;
  right: 30%;
}

.connection {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  transform-origin: left center;
  animation: pulse 2s infinite;
}

/* 现代化标题样式 */
.app-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.05em;
}

.app-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  font-weight: 400;
}

/* 现代化输入区域样式 */
.input-wrapper {
  position: relative;
  background: var(--glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
}

.input-wrapper:hover {
  box-shadow: var(--shadow-lg);
}

.input-wrapper:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow-lg);
}

.input-field {
  width: 100%;
  background: transparent;
  border: none;
  outline: none;
  font-size: 1rem;
  color: var(--text-primary);
  padding-right: 3rem;
}

.input-field::placeholder {
  color: var(--text-secondary);
}

.input-icon {
  position: absolute;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  color: var(--primary);
  transition: all 0.3s ease;
  cursor: pointer;
}

.input-icon:hover {
  transform: translateY(-50%) scale(1.2) rotate(15deg);
}

/* 现代化操作按钮 */
.action-button {
  padding: 1rem 3rem;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.action-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.action-button:hover::before {
  width: 300px;
  height: 300px;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

/* 快捷功能样式 */
.quick-actions {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  z-index: 20;
}

.quick-action {
  width: 48px;
  height: 48px;
  background: var(--glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: var(--shadow);
}

.quick-action:hover {
  background: var(--primary);
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
  border-color: transparent;
}

.quick-action svg {
  width: 24px;
  height: 24px;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.quick-action:hover svg {
  color: white;
}

/* 悬浮提示 */
.tooltip {
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--text-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  box-shadow: var(--shadow-lg);
}

.quick-action:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-title {
    font-size: 2.5rem;
  }

  .app-subtitle {
    font-size: 1rem;
  }

  .input-wrapper {
    padding: 0.875rem 1.25rem;
  }

  .action-button {
    padding: 0.875rem 2.5rem;
    font-size: 1rem;
  }

  .app-icon {
    width: 100px;
    height: 100px;
  }

  .quick-actions {
    bottom: 1rem;
    gap: 0.75rem;
  }

  .quick-action {
    width: 44px;
    height: 44px;
  }

  .gradient-orb:nth-child(1) {
    width: 400px;
    height: 400px;
    top: -150px;
    right: -50px;
  }

  .gradient-orb:nth-child(2) {
    width: 300px;
    height: 300px;
    bottom: -100px;
    left: -25px;
  }

  .gradient-orb:nth-child(3) {
    width: 350px;
    height: 350px;
  }
}

/* ========== Bento Grid 卡片流式布局样式 ========== */
@layer utilities {
  /* Bento Grid 动画 */
  @keyframes float {
    0%, 100% { transform: translate(0, 0) scale(1); }
    25% { transform: translate(-30px, 30px) scale(1.05); }
    50% { transform: translate(30px, -30px) scale(0.95); }
    75% { transform: translate(-20px, -20px) scale(1.02); }
  }

  .animate-float {
    animation: float 20s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float 20s ease-in-out infinite;
    animation-delay: 10s;
  }

  /* 卡片悬停效果 */
  .bento-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .bento-card:hover {
    transform: translateY(-4px);
  }

  /* ========== 沉淀模式样式 ========== */
  /* 沉淀模式下禁用所有动画和悬停效果 */
  .pure-mode * {
    animation: none !important;
    transition: none !important;
  }

  .pure-mode *:hover {
    transform: none !important;
    box-shadow: none !important;
    background-color: inherit !important;
  }

  /* 沉淀模式下的文本样式优化 */
  .pure-mode .prose {
    line-height: 1.8;
  }

  .pure-mode .prose p {
    margin-bottom: 1.2em;
    text-align: justify;
    white-space: pre-wrap;
    word-break: break-word;
  }

  /* 确保换行符正确显示 */
  .pure-mode .prose * {
    white-space: pre-wrap;
  }

  /* 优化代码块在沉淀模式下的显示 */
  .pure-mode .prose pre {
    white-space: pre;
    overflow-x: auto;
  }

  .pure-mode .prose code {
    white-space: pre;
  }

  /* 禁用所有动画效果 */
  .pure-mode .bg-animation,
  .pure-mode .gradient-orb,
  .pure-mode .animate-float,
  .pure-mode .animate-float-delayed,
  .pure-mode .animate-pulse,
  .pure-mode .animate-spin,
  .pure-mode .animate-bounce,
  .pure-mode .modern-pulse,
  .pure-mode .skeleton,
  .pure-mode .dot-flashing,
  .pure-mode .fade-in-up,
  .pure-mode .card-expand {
    display: none !important;
  }

  /* 禁用所有闪烁效果 */
  .pure-mode [class*="animate-"],
  .pure-mode [class*="pulse"],
  .pure-mode [class*="flash"],
  .pure-mode [class*="blink"] {
    animation: none !important;
  }

  /* 移除加载状态的闪烁 */
  .pure-mode .loading,
  .pure-mode .spinner,
  .pure-mode [class*="loading"] {
    animation: none !important;
  }

  /* 毛玻璃效果增强 */
  .glass-effect {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-effect-strong {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* 平滑滚动条 */
  .smooth-scroll::-webkit-scrollbar {
    width: 12px;
  }

  .smooth-scroll::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 6px;
  }

  .smooth-scroll::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 6px;
    transition: background 0.2s;
  }

  .smooth-scroll::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* 内容加载动画 */
  @keyframes content-fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .content-fade-in {
    animation: content-fade-in 0.5s ease-out;
  }
  
  /* 卡片展开动画 */
  @keyframes card-slide-down {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .card-slide-down {
    animation: card-slide-down 0.5s ease-out;
  }

  /* 行截断 */
  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 平滑滚动样式 - 优化性能和体验 */
  .smooth-scroll {
    scroll-behavior: smooth;
    /* 启用硬件加速 */
    transform: translateZ(0);
    /* 优化滚动性能 */
    will-change: scroll-position;
    /* 更好的滚动惯性 */
    -webkit-overflow-scrolling: touch;
  }

  /* 禁用过度滚动弹性效果 */
  .no-overscroll {
    overscroll-behavior: none;
  }

  /* 流式输出动画 */
  @keyframes stream-typing {
    from {
      opacity: 0.5;
    }
    to {
      opacity: 1;
    }
  }

  .streaming-content {
    animation: stream-typing 0.5s ease-in-out infinite alternate;
  }

  /* 改进的卡片动画 */
  @keyframes card-appear {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .card-appear {
    animation: card-appear 0.6s ease-out;
  }

  /* 优化的节流更新动画 */
  @keyframes content-stream-in {
    from {
      opacity: 0.3;
      filter: blur(1px);
    }
    to {
      opacity: 1;
      filter: blur(0);
    }
  }

  .streaming-text {
    animation: content-stream-in 0.3s ease-out;
  }
}
